// Simple test to verify our React components can be imported
const React = require('react');

// Test imports
try {
  console.log('Testing component imports...');
  
  // Test if we can require our main components
  const fs = require('fs');
  
  // Check if main files exist
  const filesToCheck = [
    'src/App.js',
    'src/index.js',
    'src/pages/Login.js',
    'src/pages/Dashboard.js',
    'src/pages/SignalMap.js',
    'src/pages/UserManagement.js',
    'src/pages/Analytics.js',
    'src/components/Layout.js',
    'src/components/ProtectedRoute.js',
    'src/components/StatsCards.js',
    'src/components/SignalModal.js',
    'src/components/UserModals.js',
    'src/contexts/AuthContext.js',
    'src/services/web/firebaseConfig.js',
    'src/services/web/authService.js',
    'src/services/web/firestoreService.js',
    'src/services/web/userService.js'
  ];
  
  console.log('Checking file existence...');
  filesToCheck.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`✓ ${file} exists`);
    } else {
      console.log(`✗ ${file} missing`);
    }
  });
  
  console.log('\nChecking CSS files...');
  const cssFiles = [
    'src/index.css',
    'src/App.css',
    'src/pages/Login.css',
    'src/pages/Dashboard.css',
    'src/pages/SignalMap.css',
    'src/pages/UserManagement.css',
    'src/pages/Analytics.css',
    'src/components/Layout.css',
    'src/components/StatsCards.css',
    'src/components/SignalModal.css',
    'src/components/UserModals.css'
  ];
  
  cssFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`✓ ${file} exists`);
    } else {
      console.log(`✗ ${file} missing`);
    }
  });
  
  console.log('\nChecking public files...');
  const publicFiles = [
    'public/index.html',
    'public/manifest.json'
  ];
  
  publicFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`✓ ${file} exists`);
    } else {
      console.log(`✗ ${file} missing`);
    }
  });
  
  console.log('\n✅ File structure test completed!');
  
} catch (error) {
  console.error('❌ Error during testing:', error.message);
}
