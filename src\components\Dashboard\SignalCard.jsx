import React, { useState } from 'react';
import { MapPin, Calendar, User, AlertCircle, Edit3, Save, X } from 'lucide-react';
import { updateSignal, getCollectors } from '../../firebase/firestore';

const SignalCard = ({ signal, onUpdate }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    status: signal.status,
    priority: signal.priority,
    adminNotes: signal.adminNotes || '',
    assignedTo: signal.assignedTo || ''
  });
  const [collectors, setCollectors] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  React.useEffect(() => {
    if (isEditing) {
      loadCollectors();
    }
  }, [isEditing]);

  const loadCollectors = async () => {
    try {
      const collectorsData = await getCollectors();
      setCollectors(collectorsData);
    } catch (err) {
      console.error('Error loading collectors:', err);
      setError('Failed to load collectors');
    }
  };

  const handleSave = async () => {
    setLoading(true);
    setError(null);
    
    try {
      await updateSignal(signal.id, editData);
      setIsEditing(false);
      if (onUpdate) onUpdate();
    } catch (error) {
      console.error('Error updating signal:', error);
      alert('Error updating signal: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'normal':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (timestamp) => {
    if (!timestamp) return 'N/A';
    return timestamp.toDate().toLocaleDateString() + ' ' + timestamp.toDate().toLocaleTimeString();
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {signal.description}
          </h3>
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <div className="flex items-center">
              <MapPin className="h-4 w-4 mr-1" />
              {signal.location?.address || 'No address'}
            </div>
            <div className="flex items-center">
              <User className="h-4 w-4 mr-1" />
              {signal.userEmail}
            </div>
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-1" />
              {formatDate(signal.createdAt)}
            </div>
          </div>
        </div>
        <div className="flex flex-col space-y-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(signal.status)}`}>
            {signal.status.replace('_', ' ').toUpperCase()}
          </span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(signal.priority)}`}>
            {signal.priority.toUpperCase()}
          </span>
        </div>
      </div>

      {signal.imageUrl && (
        <div className="mb-4">
          <img
            src={signal.imageUrl}
            alt="Signal"
            className="w-full h-48 object-cover rounded-lg cursor-pointer"
            onClick={() => window.open(signal.imageUrl, '_blank')}
          />
        </div>
      )}

      <div className="flex justify-between items-center">
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="text-green-600 hover:text-green-800 text-sm font-medium"
        >
          {showDetails ? 'Hide Details' : 'Show Details'}
        </button>
        <button
          onClick={() => setEditMode(!editMode)}
          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
        >
          {editMode ? 'Cancel' : 'Edit'}
        </button>
      </div>

      {showDetails && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Assigned To:</span>
              <p className="text-gray-600">{signal.assignedTo || 'Unassigned'}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Report Source:</span>
              <p className="text-gray-600">{signal.reportSource}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Location Accuracy:</span>
              <p className="text-gray-600">{signal.location?.accuracy ? `${signal.location.accuracy}m` : 'N/A'}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Last Updated:</span>
              <p className="text-gray-600">{formatDate(signal.updatedAt)}</p>
            </div>
            {signal.adminNotes && (
              <div className="col-span-2">
                <span className="font-medium text-gray-700">Admin Notes:</span>
                <p className="text-gray-600">{signal.adminNotes}</p>
              </div>
            )}
          </div>
        </div>
      )}

      {editMode && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-2 gap-4">
            <div></div>