{"name": "clean-spot-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "firebase": "^11.10.0", "leaflet": "^1.9.4", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.59.0", "react-leaflet": "^5.0.0", "react-router-dom": "^7.6.3", "recharts": "^3.0.2", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/leaflet": "^1.9.19", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "vite": "^7.0.0"}}