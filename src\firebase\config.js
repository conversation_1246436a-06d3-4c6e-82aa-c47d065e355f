import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';

export const firebaseConfig = {
  apiKey: "AIzaSyDVvf7S0TgyzBWKmwNdRz3ffGFRTCeXPro",
  authDomain: "cleanspotyn.firebaseapp.com",
  projectId: "cleanspotyn",
  storageBucket: "cleanspotyn.appspot.com",
  messagingSenderId: "1066328077987",
  appId: "1:1066328077987:web:493c39c6bec6cf7aba7116",
  measurementId: "G-6PQPTTGVMZ"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

export default app;